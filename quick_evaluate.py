#!/usr/bin/env python3
"""
快速模型评估脚本
用于快速评估任何模型结果目录
"""

import os
import json
import numpy as np
import argparse
from pathlib import Path

def quick_evaluate(model_dir):
    """快速评估模型"""
    model_dir = Path(model_dir)
    results_file = model_dir / "test_results.json"
    
    if not results_file.exists():
        print(f"错误: 找不到测试结果文件 {results_file}")
        return
    
    # 加载测试结果
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    confusion_mat = np.array(results['confusion_matrix'])
    class_accuracies = results['class_accuracies']
    overall_accuracy = results['test_acc']
    
    print(f"\n{'='*60}")
    print(f"模型评估结果: {model_dir.name}")
    print(f"{'='*60}")
    
    # 基本信息
    print(f"总体准确率: {overall_accuracy:.2f}%")
    print(f"最佳轮次: {results.get('best_epoch', 'N/A')}")
    print(f"最佳验证准确率: {results.get('best_val_acc', 'N/A'):.4f}")
    
    # 类别准确率
    print(f"\n各类别准确率:")
    print(f"  正常: {class_accuracies.get('normal', 0):.2f}%")
    print(f"  早期圆锥角膜: {class_accuracies.get('e-kc', 0):.2f}%")
    print(f"  圆锥角膜: {class_accuracies.get('kc', 0):.2f}%")
    
    # 混淆矩阵
    print(f"\n混淆矩阵:")
    print(f"        预测")
    print(f"真实    正常  E-KC   KC")
    print(f"正常   {confusion_mat[0,0]:4d} {confusion_mat[0,1]:4d} {confusion_mat[0,2]:4d}")
    print(f"E-KC   {confusion_mat[1,0]:4d} {confusion_mat[1,1]:4d} {confusion_mat[1,2]:4d}")
    print(f"KC     {confusion_mat[2,0]:4d} {confusion_mat[2,1]:4d} {confusion_mat[2,2]:4d}")
    
    # 二分类评估 (正常 vs 异常)
    normal_correct = confusion_mat[0, 0]
    normal_wrong = confusion_mat[0, 1] + confusion_mat[0, 2]
    abnormal_wrong = confusion_mat[1, 0] + confusion_mat[2, 0]
    abnormal_correct = confusion_mat[1, 1] + confusion_mat[1, 2] + confusion_mat[2, 1] + confusion_mat[2, 2]
    
    binary_accuracy = (normal_correct + abnormal_correct) / confusion_mat.sum()
    
    print(f"\n二分类评估 (正常 vs 异常):")
    print(f"  准确率: {binary_accuracy:.4f}")
    print(f"  正常类正确: {normal_correct}, 错误: {normal_wrong}")
    print(f"  异常类正确: {abnormal_correct}, 错误: {abnormal_wrong}")
    
    # E-KC vs KC评估
    ekc_kc_matrix = confusion_mat[1:3, 1:3]
    ekc_kc_accuracy = np.trace(ekc_kc_matrix) / ekc_kc_matrix.sum()
    
    print(f"\nE-KC vs KC评估:")
    print(f"  准确率: {ekc_kc_accuracy:.4f}")
    print(f"  E-KC正确: {confusion_mat[1,1]}, 被误分为KC: {confusion_mat[1,2]}")
    print(f"  KC正确: {confusion_mat[2,2]}, 被误分为E-KC: {confusion_mat[2,1]}")
    
    # 关键问题分析
    print(f"\n关键问题分析:")
    ekc_to_normal = confusion_mat[1, 0]
    kc_to_normal = confusion_mat[2, 0]
    kc_to_ekc = confusion_mat[2, 1]
    
    print(f"  E-KC被误分为正常: {ekc_to_normal} 例")
    print(f"  KC被误分为正常: {kc_to_normal} 例")
    print(f"  KC被误分为E-KC: {kc_to_ekc} 例")
    
    total_misclassified = confusion_mat.sum() - np.trace(confusion_mat)
    print(f"  总误分类样本: {total_misclassified} 例")
    
    # 性能总结
    print(f"\n性能总结:")
    best_class = max(class_accuracies, key=class_accuracies.get)
    worst_class = min(class_accuracies, key=class_accuracies.get)
    print(f"  表现最好的类别: {best_class} ({class_accuracies[best_class]:.2f}%)")
    print(f"  表现最差的类别: {worst_class} ({class_accuracies[worst_class]:.2f}%)")
    
    return {
        'overall_accuracy': overall_accuracy,
        'binary_accuracy': binary_accuracy,
        'ekc_kc_accuracy': ekc_kc_accuracy,
        'class_accuracies': class_accuracies,
        'confusion_matrix': confusion_mat.tolist(),
        'best_class': best_class,
        'worst_class': worst_class
    }

def compare_models(model_dirs):
    """比较多个模型"""
    results = {}
    
    print(f"\n{'='*80}")
    print(f"模型对比分析")
    print(f"{'='*80}")
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            results[model_dir] = quick_evaluate(model_dir)
        else:
            print(f"警告: 模型目录不存在 {model_dir}")
    
    if len(results) > 1:
        print(f"\n{'='*80}")
        print(f"模型对比总结")
        print(f"{'='*80}")
        
        print(f"{'模型':<40} {'总体准确率':<12} {'二分类准确率':<12} {'KC准确率':<10}")
        print(f"{'-'*80}")
        
        for model_dir, result in results.items():
            model_name = Path(model_dir).name
            if len(model_name) > 35:
                model_name = model_name[:32] + "..."
            
            kc_acc = result['class_accuracies'].get('kc', 0)
            print(f"{model_name:<40} {result['overall_accuracy']:<12.2f} {result['binary_accuracy']:<12.4f} {kc_acc:<10.2f}")
        
        # 找出最佳模型
        best_overall = max(results.items(), key=lambda x: x[1]['overall_accuracy'])
        best_kc = max(results.items(), key=lambda x: x[1]['class_accuracies'].get('kc', 0))
        
        print(f"\n最佳总体准确率: {Path(best_overall[0]).name} ({best_overall[1]['overall_accuracy']:.2f}%)")
        print(f"最佳KC准确率: {Path(best_kc[0]).name} ({best_kc[1]['class_accuracies'].get('kc', 0):.2f}%)")

def main():
    parser = argparse.ArgumentParser(description='快速模型评估')
    parser.add_argument('model_dirs', nargs='+', help='模型结果目录路径')
    parser.add_argument('--compare', action='store_true', help='比较多个模型')
    
    args = parser.parse_args()
    
    if args.compare or len(args.model_dirs) > 1:
        compare_models(args.model_dirs)
    else:
        quick_evaluate(args.model_dirs[0])

if __name__ == "__main__":
    main()
