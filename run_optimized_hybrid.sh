#!/bin/bash

# 优化的混合决策训练脚本
# 专门针对KC分类性能优化

echo "🚀 开始优化的混合决策训练..."

# 设置基本参数
TRAIN_CSV="data/train.csv"
VAL_CSV="data/val.csv"
TEST_CSV="data/test.csv"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BASE_DIR="hybrid_optimized_${TIMESTAMP}"

# GPU设置
export CUDA_VISIBLE_DEVICES=0

# 创建输出目录
mkdir -p "$BASE_DIR"

echo "📊 训练配置:"
echo "  - 专门优化KC分类性能"
echo "  - KC权重增强: 4.8 (原4.0 * 1.2)"
echo "  - E-KC权重降低: 6.4 (原8.0 * 0.8)"
echo "  - 添加KC特定损失增强"
echo "  - 使用GPU: $CUDA_VISIBLE_DEVICES"

# 运行优化的混合决策训练
python train_hybrid_decision.py \
    --train_csv "$TRAIN_CSV" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "${BASE_DIR}/hybrid_optimized" \
    --device cuda:0 \
    --epochs 20 \
    --lr 0.0001 \
    --weight_decay 1e-4 \
    --proto_counts "2,4,2" \
    --feature_dim 512 \
    --inner_lr 0.3 \
    --inner_steps 2 \
    --pretrained \
    --dropout 0.4 \
    --hybrid_type basic \
    --n_way 3 \
    --n_shot 5 \
    --n_query 15 \
    --tasks_per_epoch 20 \
    --early_kc_weight 6.4 \
    --kc_weight 4.8 \
    --focal_gamma 2.5 \
    --use_balanced_task_sampler \
    --kc_shot_multiplier 3.0 \
    --early_kc_shot_multiplier 1.5 \
    --val_frequency 2 \
    --early_stopping 3 \
    --seed 42

if [ $? -eq 0 ]; then
    echo "✅ 优化混合决策训练完成!"
    echo "📁 结果保存在: ${BASE_DIR}/hybrid_optimized"
    
    # 显示结果摘要
    echo ""
    echo "📊 训练结果摘要:"
    if [ -f "${BASE_DIR}/hybrid_optimized/hybrid_test_results.json" ]; then
        python -c "
import json
with open('${BASE_DIR}/hybrid_optimized/hybrid_test_results.json', 'r') as f:
    results = json.load(f)
print(f'总体准确率: {results[\"test_acc\"]:.1f}%')
print('类别准确率:')
for class_name, acc in results['class_accuracies'].items():
    print(f'  {class_name}: {acc:.1f}%')
"
    fi
else
    echo "❌ 优化混合决策训练失败!"
    exit 1
fi

echo ""
echo "🎯 性能对比建议:"
echo "1. 如果KC准确率 > 85%: 优化成功，建议使用此模型"
echo "2. 如果KC准确率 80-85%: 性能良好，可考虑进一步调优"
echo "3. 如果KC准确率 < 80%: 需要调整参数或尝试其他方法"
echo ""
echo "💡 下一步建议:"
echo "- 比较与原始模型(KC: 77.5%)的性能差异"
echo "- 如果性能提升显著，可以部署使用"
echo "- 如果性能提升有限，建议使用原始模型+校准"
