#!/usr/bin/env python3
"""
优化模型全面评估脚本
对run_optimized.sh训练出来的模型进行全面评估，包括：
1. 二分类评估（正常vs异常）
2. 早期圆锥角膜与圆锥角膜单独评估
3. 三分类结果评估图像
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc,
    precision_recall_curve, average_precision_score
)
from sklearn.preprocessing import label_binarize
import torch
import torch.nn.functional as F
from collections import defaultdict
import argparse
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ModelEvaluator:
    def __init__(self, model_dir):
        """
        初始化评估器
        Args:
            model_dir: 模型结果目录路径
        """
        self.model_dir = Path(model_dir)
        self.results_file = self.model_dir / "test_results.json"
        self.output_dir = self.model_dir / "evaluation_results"
        self.output_dir.mkdir(exist_ok=True)

        # 类别映射
        self.class_names = ['normal', 'e-kc', 'kc']
        self.class_names_cn = ['正常', '早期圆锥角膜', '圆锥角膜']

        # 加载测试结果
        self.load_test_results()

    def load_test_results(self):
        """加载测试结果"""
        if not self.results_file.exists():
            raise FileNotFoundError(f"测试结果文件不存在: {self.results_file}")

        with open(self.results_file, 'r') as f:
            self.results = json.load(f)

        # 提取混淆矩阵和类别准确率
        self.confusion_mat = np.array(self.results['confusion_matrix'])
        self.class_accuracies = self.results['class_accuracies']
        self.overall_accuracy = self.results['test_acc']

        print(f"加载模型结果: {self.model_dir}")
        print(f"总体准确率: {self.overall_accuracy:.2f}%")
        print(f"类别准确率: {self.class_accuracies}")

    def binary_classification_evaluation(self):
        """
        二分类评估：正常 vs 异常（E-KC + KC）
        """
        print("\n" + "="*50)
        print("二分类评估：正常 vs 异常")
        print("="*50)

        # 将三分类混淆矩阵转换为二分类
        # normal vs abnormal (e-kc + kc)
        cm_3class = self.confusion_mat

        # 重新组织混淆矩阵：[normal, abnormal]
        # normal: 第0行第0列
        # abnormal: 第1,2行的总和 vs 第1,2列的总和
        normal_correct = cm_3class[0, 0]  # 正常预测为正常
        normal_wrong = cm_3class[0, 1] + cm_3class[0, 2]  # 正常预测为异常
        abnormal_wrong = cm_3class[1, 0] + cm_3class[2, 0]  # 异常预测为正常
        abnormal_correct = cm_3class[1, 1] + cm_3class[1, 2] + cm_3class[2, 1] + cm_3class[2, 2]  # 异常预测为异常

        # 二分类混淆矩阵
        cm_binary = np.array([
            [normal_correct, normal_wrong],      # 正常类
            [abnormal_wrong, abnormal_correct]   # 异常类
        ])

        # 计算真实标签和预测标签
        y_true_binary = []
        y_pred_binary = []

        # 从混淆矩阵重构标签
        for i in range(3):
            for j in range(3):
                count = cm_3class[i, j]
                true_label = 0 if i == 0 else 1  # 0=normal, 1=abnormal
                pred_label = 0 if j == 0 else 1  # 0=normal, 1=abnormal
                y_true_binary.extend([true_label] * count)
                y_pred_binary.extend([pred_label] * count)

        y_true_binary = np.array(y_true_binary)
        y_pred_binary = np.array(y_pred_binary)

        # 计算评估指标
        accuracy = accuracy_score(y_true_binary, y_pred_binary)
        precision = precision_score(y_true_binary, y_pred_binary, average='weighted')
        recall = recall_score(y_true_binary, y_pred_binary, average='weighted')
        f1 = f1_score(y_true_binary, y_pred_binary, average='weighted')

        # 每个类别的指标
        precision_per_class = precision_score(y_true_binary, y_pred_binary, average=None)
        recall_per_class = recall_score(y_true_binary, y_pred_binary, average=None)
        f1_per_class = f1_score(y_true_binary, y_pred_binary, average=None)

        print(f"二分类准确率: {accuracy:.4f}")
        print(f"二分类精确率: {precision:.4f}")
        print(f"二分类召回率: {recall:.4f}")
        print(f"二分类F1分数: {f1:.4f}")
        print()
        print("各类别详细指标:")
        print(f"正常类 - 精确率: {precision_per_class[0]:.4f}, 召回率: {recall_per_class[0]:.4f}, F1: {f1_per_class[0]:.4f}")
        print(f"异常类 - 精确率: {precision_per_class[1]:.4f}, 召回率: {recall_per_class[1]:.4f}, F1: {f1_per_class[1]:.4f}")

        # 保存二分类结果
        binary_results = {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1),
            'confusion_matrix': cm_binary.tolist(),
            'per_class_metrics': {
                'normal': {
                    'precision': float(precision_per_class[0]),
                    'recall': float(recall_per_class[0]),
                    'f1_score': float(f1_per_class[0])
                },
                'abnormal': {
                    'precision': float(precision_per_class[1]),
                    'recall': float(recall_per_class[1]),
                    'f1_score': float(f1_per_class[1])
                }
            }
        }

        with open(self.output_dir / "binary_classification_results.json", 'w', encoding='utf-8') as f:
            json.dump(binary_results, f, indent=2, ensure_ascii=False)

        # 绘制二分类混淆矩阵
        self.plot_binary_confusion_matrix(cm_binary)

        # 绘制二分类指标对比图
        self.plot_binary_metrics(binary_results)

        return binary_results

    def plot_binary_confusion_matrix(self, cm_binary):
        """绘制二分类混淆矩阵"""
        plt.figure(figsize=(8, 6))

        # 计算百分比
        cm_percent = cm_binary.astype('float') / cm_binary.sum(axis=1)[:, np.newaxis] * 100

        # 创建标注
        annotations = []
        for i in range(2):
            row = []
            for j in range(2):
                row.append(f'{cm_binary[i,j]}\n({cm_percent[i,j]:.1f}%)')
            annotations.append(row)

        sns.heatmap(cm_binary, annot=annotations, fmt='', cmap='Blues',
                   xticklabels=['正常', '异常'], yticklabels=['正常', '异常'],
                   cbar_kws={'label': '样本数量'})

        plt.title('二分类混淆矩阵（正常 vs 异常）', fontsize=14, fontweight='bold')
        plt.xlabel('预测类别', fontsize=12)
        plt.ylabel('真实类别', fontsize=12)
        plt.tight_layout()
        plt.savefig(self.output_dir / "binary_confusion_matrix.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_binary_metrics(self, binary_results):
        """绘制二分类指标对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 整体指标
        metrics = ['准确率', '精确率', '召回率', 'F1分数']
        values = [
            binary_results['accuracy'],
            binary_results['precision'],
            binary_results['recall'],
            binary_results['f1_score']
        ]

        bars1 = ax1.bar(metrics, values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
        ax1.set_title('二分类整体性能指标', fontsize=14, fontweight='bold')
        ax1.set_ylabel('分数', fontsize=12)
        ax1.set_ylim(0, 1)

        # 添加数值标签
        for bar, value in zip(bars1, values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # 各类别指标对比
        classes = ['正常', '异常']
        precision_vals = [binary_results['per_class_metrics']['normal']['precision'],
                         binary_results['per_class_metrics']['abnormal']['precision']]
        recall_vals = [binary_results['per_class_metrics']['normal']['recall'],
                      binary_results['per_class_metrics']['abnormal']['recall']]
        f1_vals = [binary_results['per_class_metrics']['normal']['f1_score'],
                  binary_results['per_class_metrics']['abnormal']['f1_score']]

        x = np.arange(len(classes))
        width = 0.25

        bars2 = ax2.bar(x - width, precision_vals, width, label='精确率', color='#1f77b4')
        bars3 = ax2.bar(x, recall_vals, width, label='召回率', color='#ff7f0e')
        bars4 = ax2.bar(x + width, f1_vals, width, label='F1分数', color='#2ca02c')

        ax2.set_title('各类别性能指标对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('分数', fontsize=12)
        ax2.set_xlabel('类别', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(classes)
        ax2.legend()
        ax2.set_ylim(0, 1)

        # 添加数值标签
        for bars in [bars2, bars3, bars4]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(self.output_dir / "binary_metrics_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def ekc_kc_evaluation(self):
        """
        早期圆锥角膜与圆锥角膜单独评估
        """
        print("\n" + "="*50)
        print("早期圆锥角膜与圆锥角膜单独评估")
        print("="*50)

        # 从三分类混淆矩阵中提取E-KC和KC的部分
        cm_3class = self.confusion_mat

        # 提取E-KC和KC的混淆矩阵 (索引1和2)
        # cm_3class的结构: [normal, e-kc, kc]
        ekc_kc_matrix = cm_3class[1:3, 1:3]  # 只取e-kc和kc的部分

        # 重构标签用于计算指标
        y_true_ekc_kc = []
        y_pred_ekc_kc = []

        # 从混淆矩阵重构标签 (只考虑E-KC和KC)
        for i in range(1, 3):  # 1=e-kc, 2=kc
            for j in range(1, 3):
                count = cm_3class[i, j]
                true_label = i - 1  # 转换为0(e-kc), 1(kc)
                pred_label = j - 1  # 转换为0(e-kc), 1(kc)
                y_true_ekc_kc.extend([true_label] * count)
                y_pred_ekc_kc.extend([pred_label] * count)

        y_true_ekc_kc = np.array(y_true_ekc_kc)
        y_pred_ekc_kc = np.array(y_pred_ekc_kc)

        # 计算评估指标
        accuracy = accuracy_score(y_true_ekc_kc, y_pred_ekc_kc)
        precision = precision_score(y_true_ekc_kc, y_pred_ekc_kc, average='weighted')
        recall = recall_score(y_true_ekc_kc, y_pred_ekc_kc, average='weighted')
        f1 = f1_score(y_true_ekc_kc, y_pred_ekc_kc, average='weighted')

        # 每个类别的指标
        precision_per_class = precision_score(y_true_ekc_kc, y_pred_ekc_kc, average=None)
        recall_per_class = recall_score(y_true_ekc_kc, y_pred_ekc_kc, average=None)
        f1_per_class = f1_score(y_true_ekc_kc, y_pred_ekc_kc, average=None)

        print(f"E-KC vs KC 分类准确率: {accuracy:.4f}")
        print(f"E-KC vs KC 分类精确率: {precision:.4f}")
        print(f"E-KC vs KC 分类召回率: {recall:.4f}")
        print(f"E-KC vs KC 分类F1分数: {f1:.4f}")
        print()
        print("各类别详细指标:")
        print(f"早期圆锥角膜 - 精确率: {precision_per_class[0]:.4f}, 召回率: {recall_per_class[0]:.4f}, F1: {f1_per_class[0]:.4f}")
        print(f"圆锥角膜 - 精确率: {precision_per_class[1]:.4f}, 召回率: {recall_per_class[1]:.4f}, F1: {f1_per_class[1]:.4f}")

        # 计算原始类别准确率
        ekc_total = cm_3class[1, :].sum()
        kc_total = cm_3class[2, :].sum()
        ekc_correct = cm_3class[1, 1]
        kc_correct = cm_3class[2, 2]

        ekc_accuracy = ekc_correct / ekc_total if ekc_total > 0 else 0
        kc_accuracy = kc_correct / kc_total if kc_total > 0 else 0

        print(f"\n原始三分类中的表现:")
        print(f"早期圆锥角膜准确率: {ekc_accuracy:.4f} ({ekc_correct}/{ekc_total})")
        print(f"圆锥角膜准确率: {kc_accuracy:.4f} ({kc_correct}/{kc_total})")

        # 分析混淆情况
        ekc_to_kc = cm_3class[1, 2]  # E-KC被误分为KC
        kc_to_ekc = cm_3class[2, 1]  # KC被误分为E-KC
        ekc_to_normal = cm_3class[1, 0]  # E-KC被误分为正常
        kc_to_normal = cm_3class[2, 0]  # KC被误分为正常

        print(f"\n混淆分析:")
        print(f"早期圆锥角膜被误分为圆锥角膜: {ekc_to_kc} 例")
        print(f"圆锥角膜被误分为早期圆锥角膜: {kc_to_ekc} 例")
        print(f"早期圆锥角膜被误分为正常: {ekc_to_normal} 例")
        print(f"圆锥角膜被误分为正常: {kc_to_normal} 例")

        # 保存E-KC vs KC评估结果
        ekc_kc_results = {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1),
            'confusion_matrix': ekc_kc_matrix.tolist(),
            'per_class_metrics': {
                'e-kc': {
                    'precision': float(precision_per_class[0]),
                    'recall': float(recall_per_class[0]),
                    'f1_score': float(f1_per_class[0]),
                    'original_accuracy': float(ekc_accuracy)
                },
                'kc': {
                    'precision': float(precision_per_class[1]),
                    'recall': float(recall_per_class[1]),
                    'f1_score': float(f1_per_class[1]),
                    'original_accuracy': float(kc_accuracy)
                }
            },
            'confusion_analysis': {
                'ekc_to_kc': int(ekc_to_kc),
                'kc_to_ekc': int(kc_to_ekc),
                'ekc_to_normal': int(ekc_to_normal),
                'kc_to_normal': int(kc_to_normal)
            }
        }

        with open(self.output_dir / "ekc_kc_evaluation_results.json", 'w', encoding='utf-8') as f:
            json.dump(ekc_kc_results, f, indent=2, ensure_ascii=False)

        # 绘制E-KC vs KC混淆矩阵
        self.plot_ekc_kc_confusion_matrix(ekc_kc_matrix)

        # 绘制E-KC vs KC指标对比图
        self.plot_ekc_kc_metrics(ekc_kc_results)

        # 绘制混淆分析图
        self.plot_confusion_analysis(ekc_kc_results['confusion_analysis'])

        return ekc_kc_results

    def plot_ekc_kc_confusion_matrix(self, ekc_kc_matrix):
        """绘制E-KC vs KC混淆矩阵"""
        plt.figure(figsize=(8, 6))

        # 计算百分比
        cm_percent = ekc_kc_matrix.astype('float') / ekc_kc_matrix.sum(axis=1)[:, np.newaxis] * 100

        # 创建标注
        annotations = []
        for i in range(2):
            row = []
            for j in range(2):
                row.append(f'{ekc_kc_matrix[i,j]}\n({cm_percent[i,j]:.1f}%)')
            annotations.append(row)

        sns.heatmap(ekc_kc_matrix, annot=annotations, fmt='', cmap='Oranges',
                   xticklabels=['早期圆锥角膜', '圆锥角膜'],
                   yticklabels=['早期圆锥角膜', '圆锥角膜'],
                   cbar_kws={'label': '样本数量'})

        plt.title('早期圆锥角膜 vs 圆锥角膜混淆矩阵', fontsize=14, fontweight='bold')
        plt.xlabel('预测类别', fontsize=12)
        plt.ylabel('真实类别', fontsize=12)
        plt.tight_layout()
        plt.savefig(self.output_dir / "ekc_kc_confusion_matrix.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_ekc_kc_metrics(self, ekc_kc_results):
        """绘制E-KC vs KC指标对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 整体指标
        metrics = ['准确率', '精确率', '召回率', 'F1分数']
        values = [
            ekc_kc_results['accuracy'],
            ekc_kc_results['precision'],
            ekc_kc_results['recall'],
            ekc_kc_results['f1_score']
        ]

        bars1 = ax1.bar(metrics, values, color=['#ff7f0e', '#2ca02c', '#d62728', '#9467bd'])
        ax1.set_title('E-KC vs KC 整体性能指标', fontsize=14, fontweight='bold')
        ax1.set_ylabel('分数', fontsize=12)
        ax1.set_ylim(0, 1)

        # 添加数值标签
        for bar, value in zip(bars1, values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # 各类别指标对比
        classes = ['早期圆锥角膜', '圆锥角膜']
        precision_vals = [ekc_kc_results['per_class_metrics']['e-kc']['precision'],
                         ekc_kc_results['per_class_metrics']['kc']['precision']]
        recall_vals = [ekc_kc_results['per_class_metrics']['e-kc']['recall'],
                      ekc_kc_results['per_class_metrics']['kc']['recall']]
        f1_vals = [ekc_kc_results['per_class_metrics']['e-kc']['f1_score'],
                  ekc_kc_results['per_class_metrics']['kc']['f1_score']]

        x = np.arange(len(classes))
        width = 0.25

        bars2 = ax2.bar(x - width, precision_vals, width, label='精确率', color='#ff7f0e')
        bars3 = ax2.bar(x, recall_vals, width, label='召回率', color='#2ca02c')
        bars4 = ax2.bar(x + width, f1_vals, width, label='F1分数', color='#d62728')

        ax2.set_title('各类别性能指标对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('分数', fontsize=12)
        ax2.set_xlabel('类别', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(classes)
        ax2.legend()
        ax2.set_ylim(0, 1)

        # 添加数值标签
        for bars in [bars2, bars3, bars4]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(self.output_dir / "ekc_kc_metrics_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_confusion_analysis(self, confusion_analysis):
        """绘制混淆分析图"""
        plt.figure(figsize=(12, 8))

        # 创建混淆类型和数量
        confusion_types = ['E-KC→KC', 'KC→E-KC', 'E-KC→正常', 'KC→正常']
        confusion_counts = [
            confusion_analysis['ekc_to_kc'],
            confusion_analysis['kc_to_ekc'],
            confusion_analysis['ekc_to_normal'],
            confusion_analysis['kc_to_normal']
        ]

        # 创建颜色
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']

        # 绘制柱状图
        bars = plt.bar(confusion_types, confusion_counts, color=colors, edgecolor='black', linewidth=1)

        plt.title('模型混淆分析', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('混淆类型', fontsize=12)
        plt.ylabel('样本数量', fontsize=12)

        # 添加数值标签
        for bar, count in zip(bars, confusion_counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    str(count), ha='center', va='bottom', fontweight='bold', fontsize=11)

        # 添加网格
        plt.grid(axis='y', alpha=0.3, linestyle='--')

        # 旋转x轴标签以避免重叠
        plt.xticks(rotation=45, ha='right')

        plt.tight_layout()
        plt.savefig(self.output_dir / "confusion_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()

    def three_class_evaluation(self):
        """
        三分类结果评估
        """
        print("\n" + "="*50)
        print("三分类结果评估")
        print("="*50)

        # 从混淆矩阵重构标签
        y_true_3class = []
        y_pred_3class = []

        for i in range(3):
            for j in range(3):
                count = self.confusion_mat[i, j]
                y_true_3class.extend([i] * count)
                y_pred_3class.extend([j] * count)

        y_true_3class = np.array(y_true_3class)
        y_pred_3class = np.array(y_pred_3class)

        # 计算评估指标
        accuracy = accuracy_score(y_true_3class, y_pred_3class)
        precision = precision_score(y_true_3class, y_pred_3class, average='weighted')
        recall = recall_score(y_true_3class, y_pred_3class, average='weighted')
        f1 = f1_score(y_true_3class, y_pred_3class, average='weighted')

        # 每个类别的指标
        precision_per_class = precision_score(y_true_3class, y_pred_3class, average=None)
        recall_per_class = recall_score(y_true_3class, y_pred_3class, average=None)
        f1_per_class = f1_score(y_true_3class, y_pred_3class, average=None)

        print(f"三分类准确率: {accuracy:.4f}")
        print(f"三分类精确率: {precision:.4f}")
        print(f"三分类召回率: {recall:.4f}")
        print(f"三分类F1分数: {f1:.4f}")
        print()
        print("各类别详细指标:")
        for i, class_name in enumerate(self.class_names_cn):
            print(f"{class_name} - 精确率: {precision_per_class[i]:.4f}, 召回率: {recall_per_class[i]:.4f}, F1: {f1_per_class[i]:.4f}")

        # 计算分类报告
        report = classification_report(y_true_3class, y_pred_3class,
                                     target_names=self.class_names_cn,
                                     output_dict=True)

        # 保存三分类结果
        three_class_results = {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1_score': float(f1),
            'confusion_matrix': self.confusion_mat.tolist(),
            'per_class_metrics': {
                self.class_names[i]: {
                    'precision': float(precision_per_class[i]),
                    'recall': float(recall_per_class[i]),
                    'f1_score': float(f1_per_class[i])
                } for i in range(3)
            },
            'classification_report': report
        }

        with open(self.output_dir / "three_class_evaluation_results.json", 'w', encoding='utf-8') as f:
            json.dump(three_class_results, f, indent=2, ensure_ascii=False)

        # 绘制三分类评估图像
        self.plot_three_class_confusion_matrix()
        self.plot_three_class_metrics(three_class_results)
        self.plot_class_accuracy_comparison()
        self.plot_training_history()

        return three_class_results

    def plot_three_class_confusion_matrix(self):
        """绘制三分类混淆矩阵"""
        plt.figure(figsize=(10, 8))

        # 计算百分比
        cm_percent = self.confusion_mat.astype('float') / self.confusion_mat.sum(axis=1)[:, np.newaxis] * 100

        # 创建标注
        annotations = []
        for i in range(3):
            row = []
            for j in range(3):
                row.append(f'{self.confusion_mat[i,j]}\n({cm_percent[i,j]:.1f}%)')
            annotations.append(row)

        sns.heatmap(self.confusion_mat, annot=annotations, fmt='', cmap='Blues',
                   xticklabels=self.class_names_cn, yticklabels=self.class_names_cn,
                   cbar_kws={'label': '样本数量'})

        plt.title('三分类混淆矩阵', fontsize=16, fontweight='bold')
        plt.xlabel('预测类别', fontsize=12)
        plt.ylabel('真实类别', fontsize=12)
        plt.tight_layout()
        plt.savefig(self.output_dir / "three_class_confusion_matrix.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_three_class_metrics(self, three_class_results):
        """绘制三分类指标对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 整体指标
        metrics = ['准确率', '精确率', '召回率', 'F1分数']
        values = [
            three_class_results['accuracy'],
            three_class_results['precision'],
            three_class_results['recall'],
            three_class_results['f1_score']
        ]

        bars1 = ax1.bar(metrics, values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
        ax1.set_title('三分类整体性能指标', fontsize=14, fontweight='bold')
        ax1.set_ylabel('分数', fontsize=12)
        ax1.set_ylim(0, 1)

        # 添加数值标签
        for bar, value in zip(bars1, values):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # 各类别指标对比
        precision_vals = [three_class_results['per_class_metrics'][cls]['precision'] for cls in self.class_names]
        recall_vals = [three_class_results['per_class_metrics'][cls]['recall'] for cls in self.class_names]
        f1_vals = [three_class_results['per_class_metrics'][cls]['f1_score'] for cls in self.class_names]

        x = np.arange(len(self.class_names_cn))
        width = 0.25

        bars2 = ax2.bar(x - width, precision_vals, width, label='精确率', color='#1f77b4')
        bars3 = ax2.bar(x, recall_vals, width, label='召回率', color='#ff7f0e')
        bars4 = ax2.bar(x + width, f1_vals, width, label='F1分数', color='#2ca02c')

        ax2.set_title('各类别性能指标对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('分数', fontsize=12)
        ax2.set_xlabel('类别', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(self.class_names_cn)
        ax2.legend()
        ax2.set_ylim(0, 1)

        # 添加数值标签
        for bars in [bars2, bars3, bars4]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(self.output_dir / "three_class_metrics_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_class_accuracy_comparison(self):
        """绘制类别准确率对比图"""
        plt.figure(figsize=(10, 6))

        # 提取类别准确率
        accuracies = [self.class_accuracies[cls] for cls in self.class_names]
        colors = ['#2E8B57', '#FF6347', '#4169E1']  # 绿色、红色、蓝色

        bars = plt.bar(self.class_names_cn, accuracies, color=colors, edgecolor='black', linewidth=1)

        plt.title('各类别准确率对比', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('类别', fontsize=12)
        plt.ylabel('准确率 (%)', fontsize=12)
        plt.ylim(0, 100)

        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=12)

        # 添加网格
        plt.grid(axis='y', alpha=0.3, linestyle='--')

        # 添加平均线
        avg_acc = np.mean(accuracies)
        plt.axhline(y=avg_acc, color='red', linestyle='--', alpha=0.7,
                   label=f'平均准确率: {avg_acc:.1f}%')
        plt.legend()

        plt.tight_layout()
        plt.savefig(self.output_dir / "class_accuracy_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_training_history(self):
        """绘制训练历史曲线"""
        if 'history' not in self.results:
            print("警告: 没有找到训练历史数据，跳过训练曲线绘制")
            return

        history = self.results['history']
        epochs = range(1, len(history['train_loss']) + 1)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 损失曲线
        ax1.plot(epochs, history['train_loss'], 'b-', label='训练损失', linewidth=2)
        ax1.plot(epochs, history['val_loss'], 'r-', label='验证损失', linewidth=2)
        ax1.set_title('损失曲线', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('损失')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 准确率曲线
        ax2.plot(epochs, [acc * 100 for acc in history['train_acc']], 'b-', label='训练准确率', linewidth=2)
        ax2.plot(epochs, [acc * 100 for acc in history['val_acc']], 'r-', label='验证准确率', linewidth=2)
        ax2.set_title('准确率曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('准确率 (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 对比学习损失曲线
        if 'contrastive_loss' in history:
            ax3.plot(epochs, history['contrastive_loss'], 'g-', label='对比学习损失', linewidth=2)
            ax3.set_title('对比学习损失曲线', fontsize=14, fontweight='bold')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('对比学习损失')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, '无对比学习损失数据', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('对比学习损失曲线', fontsize=14, fontweight='bold')

        # 学习率曲线
        if 'learning_rates' in history:
            ax4.plot(epochs, history['learning_rates'], 'purple', label='学习率', linewidth=2)
            ax4.set_title('学习率曲线', fontsize=14, fontweight='bold')
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('学习率')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, '无学习率数据', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('学习率曲线', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig(self.output_dir / "training_history.png", dpi=300, bbox_inches='tight')
        plt.close()

    def plot_tns_distribution(self):
        """
        绘制TNS（True/False Negative/Positive）分布图
        """
        print("\n绘制TNS分布图...")

        # 计算各类别的TP, FP, TN, FN
        cm = self.confusion_mat
        n_classes = len(self.class_names)

        # 为每个类别计算TP, FP, TN, FN
        tns_data = {}
        for i, class_name in enumerate(self.class_names):
            tp = cm[i, i]  # True Positive
            fp = cm[:, i].sum() - tp  # False Positive
            fn = cm[i, :].sum() - tp  # False Negative
            tn = cm.sum() - tp - fp - fn  # True Negative

            tns_data[class_name] = {
                'TP': int(tp),
                'FP': int(fp),
                'TN': int(tn),
                'FN': int(fn)
            }

        # 绘制TNS分布图
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        for idx, (class_name, class_name_cn) in enumerate(zip(self.class_names, self.class_names_cn)):
            data = tns_data[class_name]
            labels = ['TP', 'FP', 'TN', 'FN']
            values = [data[label] for label in labels]
            colors = ['#2E8B57', '#FF6347', '#4169E1', '#FFD700']  # 绿、红、蓝、黄

            # 创建饼图
            wedges, texts, autotexts = axes[idx].pie(values, labels=labels, colors=colors,
                                                    autopct='%1.1f%%', startangle=90)

            axes[idx].set_title(f'{class_name_cn} TNS分布', fontsize=14, fontweight='bold')

            # 添加数值标签
            for autotext, value in zip(autotexts, values):
                autotext.set_text(f'{autotext.get_text()}\n({value})')
                autotext.set_fontsize(10)
                autotext.set_fontweight('bold')

        plt.tight_layout()
        plt.savefig(self.output_dir / "tns_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 保存TNS数据
        with open(self.output_dir / "tns_distribution_data.json", 'w', encoding='utf-8') as f:
            json.dump(tns_data, f, indent=2, ensure_ascii=False)

        return tns_data

    def generate_comprehensive_report(self):
        """
        生成综合评估报告
        """
        print("\n" + "="*50)
        print("生成综合评估报告")
        print("="*50)

        # 执行所有评估
        binary_results = self.binary_classification_evaluation()
        ekc_kc_results = self.ekc_kc_evaluation()
        three_class_results = self.three_class_evaluation()
        tns_data = self.plot_tns_distribution()

        # 生成综合报告
        report = {
            'model_info': {
                'model_directory': str(self.model_dir),
                'overall_accuracy': self.overall_accuracy,
                'class_accuracies': self.class_accuracies,
                'best_epoch': self.results.get('best_epoch', 'N/A'),
                'best_val_acc': self.results.get('best_val_acc', 'N/A')
            },
            'binary_classification': binary_results,
            'ekc_kc_evaluation': ekc_kc_results,
            'three_class_evaluation': three_class_results,
            'tns_distribution': tns_data,
            'evaluation_summary': {
                'binary_accuracy': binary_results['accuracy'],
                'ekc_kc_accuracy': ekc_kc_results['accuracy'],
                'three_class_accuracy': three_class_results['accuracy'],
                'best_performing_class': max(self.class_accuracies, key=self.class_accuracies.get),
                'worst_performing_class': min(self.class_accuracies, key=self.class_accuracies.get)
            }
        }

        # 保存综合报告
        with open(self.output_dir / "comprehensive_evaluation_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # 生成文本报告
        self.generate_text_report(report)

        print(f"\n评估完成！所有结果已保存到: {self.output_dir}")
        print("\n生成的文件包括:")
        print("- comprehensive_evaluation_report.json: 综合评估报告")
        print("- evaluation_summary.txt: 文本格式评估摘要")
        print("- binary_classification_results.json: 二分类评估结果")
        print("- ekc_kc_evaluation_results.json: E-KC vs KC评估结果")
        print("- three_class_evaluation_results.json: 三分类评估结果")
        print("- tns_distribution_data.json: TNS分布数据")
        print("- 各种评估图像文件 (.png)")

        return report

    def generate_text_report(self, report):
        """
        生成文本格式的评估摘要
        """
        with open(self.output_dir / "evaluation_summary.txt", 'w', encoding='utf-8') as f:
            f.write("="*60 + "\n")
            f.write("优化模型全面评估报告\n")
            f.write("="*60 + "\n\n")

            # 模型基本信息
            f.write("模型基本信息:\n")
            f.write("-"*30 + "\n")
            f.write(f"模型目录: {report['model_info']['model_directory']}\n")
            f.write(f"总体准确率: {report['model_info']['overall_accuracy']:.2f}%\n")
            f.write(f"最佳训练轮次: {report['model_info']['best_epoch']}\n")
            f.write(f"最佳验证准确率: {report['model_info']['best_val_acc']:.4f}\n\n")

            # 类别准确率
            f.write("各类别准确率:\n")
            f.write("-"*30 + "\n")
            for class_name, class_name_cn in zip(self.class_names, self.class_names_cn):
                acc = report['model_info']['class_accuracies'][class_name]
                f.write(f"{class_name_cn}: {acc:.2f}%\n")
            f.write("\n")

            # 二分类评估
            f.write("二分类评估 (正常 vs 异常):\n")
            f.write("-"*30 + "\n")
            binary = report['binary_classification']
            f.write(f"准确率: {binary['accuracy']:.4f}\n")
            f.write(f"精确率: {binary['precision']:.4f}\n")
            f.write(f"召回率: {binary['recall']:.4f}\n")
            f.write(f"F1分数: {binary['f1_score']:.4f}\n\n")

            # E-KC vs KC评估
            f.write("早期圆锥角膜 vs 圆锥角膜评估:\n")
            f.write("-"*30 + "\n")
            ekc_kc = report['ekc_kc_evaluation']
            f.write(f"准确率: {ekc_kc['accuracy']:.4f}\n")
            f.write(f"精确率: {ekc_kc['precision']:.4f}\n")
            f.write(f"召回率: {ekc_kc['recall']:.4f}\n")
            f.write(f"F1分数: {ekc_kc['f1_score']:.4f}\n\n")

            # 三分类评估
            f.write("三分类评估:\n")
            f.write("-"*30 + "\n")
            three_class = report['three_class_evaluation']
            f.write(f"准确率: {three_class['accuracy']:.4f}\n")
            f.write(f"精确率: {three_class['precision']:.4f}\n")
            f.write(f"召回率: {three_class['recall']:.4f}\n")
            f.write(f"F1分数: {three_class['f1_score']:.4f}\n\n")

            # 评估摘要
            f.write("评估摘要:\n")
            f.write("-"*30 + "\n")
            summary = report['evaluation_summary']
            f.write(f"表现最好的类别: {summary['best_performing_class']}\n")
            f.write(f"表现最差的类别: {summary['worst_performing_class']}\n")
            f.write(f"二分类准确率: {summary['binary_accuracy']:.4f}\n")
            f.write(f"E-KC vs KC准确率: {summary['ekc_kc_accuracy']:.4f}\n")
            f.write(f"三分类准确率: {summary['three_class_accuracy']:.4f}\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化模型全面评估')
    parser.add_argument('--model_dir', type=str, required=True,
                       help='模型结果目录路径')

    args = parser.parse_args()

    try:
        # 创建评估器
        evaluator = ModelEvaluator(args.model_dir)

        # 执行全面评估
        report = evaluator.generate_comprehensive_report()

        print("\n" + "="*50)
        print("评估摘要")
        print("="*50)
        print(f"总体准确率: {report['model_info']['overall_accuracy']:.2f}%")
        print(f"二分类准确率: {report['evaluation_summary']['binary_accuracy']:.4f}")
        print(f"E-KC vs KC准确率: {report['evaluation_summary']['ekc_kc_accuracy']:.4f}")
        print(f"三分类准确率: {report['evaluation_summary']['three_class_accuracy']:.4f}")
        print(f"表现最好的类别: {report['evaluation_summary']['best_performing_class']}")
        print(f"表现最差的类别: {report['evaluation_summary']['worst_performing_class']}")

    except Exception as e:
        print(f"评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
